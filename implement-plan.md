# DermaCare Data Flow Implementation Plan

## Executive Summary

This document outlines the critical data flow gaps between the v3Integration system and the New webhook-based implementation. While the New directory successfully implements basic patient/appointment processing and webhook infrastructure, several advanced data synchronization features essential for complete business logic parity are missing.

**Critical Gap Categories:**
- Advanced Custom Field Calculations (Service counting, spending algorithms)
- Complex Financial Processing (Detailed LTV calculation, invoice processing)
- Appointment Custom Field Synchronization (Appointment-specific custom field updates)
- Service/Entity Name Resolution (Lookup functions for human-readable names)
- Timezone-Aware Processing (Proper timezone handling for dates)
- Note Management (Appointment note creation and updates)

## Priority Matrix

| Priority | Category | Impact | Complexity |
|----------|----------|---------|------------|
| **CRITICAL** | Advanced Custom Field Calculations | High | High |
| **CRITICAL** | Complex Financial Processing | High | Medium |
| **HIGH** | Appointment Custom Field Sync | Medium | Medium |
| **HIGH** | Service/Entity Name Resolution | Medium | Low |
| **MEDIUM** | Timezone-Aware Processing | Medium | Low |
| **LOW** | Note Management | Low | Low |

## Detailed Implementation Tasks

### 1. CRITICAL: Advanced Custom Field Calculations

**Missing Functions:**
- `individualServiceAppointmentCount()` - Complete implementation missing
- `individualServiceSpends()` - Complete implementation missing

**Files to Create/Modify:**
- `New/src/helpers/serviceCalculations.ts` (NEW FILE)
- `New/src/helpers/dataTransform.ts` (MODIFY - complete calculateServiceAppointmentCounts)
- `New/src/storage/customFieldSync.ts` (MODIFY - integrate service calculations)

**Implementation Details:**

#### Task 1.1: Create Service Appointment Counting Algorithm
**File:** `New/src/helpers/serviceCalculations.ts`
**Priority:** CRITICAL
**Complexity:** HIGH (20-25 dev hours)

```typescript
/**
 * Replicates v3Integration's individualServiceAppointmentCount() function
 * - Fetches all appointments for patient
 * - Gets service names from CC API
 * - Counts appointments per service
 * - Returns custom fields like "Total appointments booked for [Service Name]"
 */
export async function calculateIndividualServiceAppointmentCounts(
  ccPatient: GetCCPatientType
): Promise<Record<string, number>>
```

**Business Logic:**
1. Check if patient has appointments (ccPatient.appointments.length > 0)
2. Fetch detailed appointment data using ccClient.appointment.getMultiple()
3. Extract service IDs from each appointment
4. Fetch service names using ccClient.service.getMultiple()
5. Count appointments per service
6. Return object with keys like "Total appointments booked for [Service Name]"

#### Task 1.2: Create Service Spending Calculation Algorithm
**File:** `New/src/helpers/serviceCalculations.ts`
**Priority:** CRITICAL
**Complexity:** HIGH (20-25 dev hours)

```typescript
/**
 * Replicates v3Integration's individualServiceSpends() function
 * - Fetches all invoices for patient
 * - Processes invoice positions (line items)
 * - Calculates spending per service/position
 * - Returns custom fields like "Total amount paid for [Service Name]"
 */
export async function calculateIndividualServiceSpending(
  ccPatient: GetCCPatientType
): Promise<Record<string, number>>
```

**Business Logic:**
1. Check if patient has invoices (ccPatient.invoices.length > 0)
2. Fetch detailed invoice data using ccClient.invoice.getMultiple()
3. Process invoice.positions array for each invoice
4. Sum position.gross amounts by position.name
5. Return object with keys like "Total amount paid for [Service Name]"

#### Task 1.3: Integrate Service Calculations into Custom Field Sync
**File:** `New/src/storage/customFieldSync.ts`
**Priority:** CRITICAL
**Complexity:** MEDIUM (10-15 dev hours)

**Modifications:**
- Update `generateAdvancedCustomFieldData()` to call service calculation functions
- Integrate service counts and spending into custom field data object
- Ensure proper error handling and fallbacks

### 2. CRITICAL: Complex Financial Processing

**Missing Functions:**
- Enhanced `syncLtv()` with complex payment validation
- Detailed `syncInvoice()` with position processing
- Enhanced `syncLastPayment()` with timezone handling

**Files to Modify:**
- `New/src/storage/financialData.ts` (ENHANCE existing functions)
- `New/src/processors/invoicePaymentProcessor.ts` (ENHANCE payment processing)

#### Task 2.1: Enhance LTV Calculation with Payment Validation
**File:** `New/src/storage/financialData.ts`
**Priority:** CRITICAL
**Complexity:** MEDIUM (15-20 dev hours)

**Enhancement Details:**
- Implement complex payment filtering: `!payment.canceled && !payment.reversedBy && !payment.reverses && payment.gross > 0`
- Add patient ID validation: `payment.patient === ccPatient.id`
- Update `calculatePatientFinancialMetrics()` to use enhanced validation

#### Task 2.2: Enhance Invoice Processing with Position Details
**File:** `New/src/processors/invoicePaymentProcessor.ts`
**Priority:** CRITICAL
**Complexity:** MEDIUM (15-20 dev hours)

**Enhancement Details:**
- Process invoice.positions array with discount calculations
- Extract invoice.diagnoses information
- Fetch practitioner names using ccClient.user.get()
- Calculate detailed totals: `totalAmount - totalDiscount - invoice.discount`
- Update custom fields: "Latest Products", "Latest Diagnosis", "Latest Treated By"

#### Task 2.3: Enhance Payment Processing with Timezone Handling
**File:** `New/src/processors/invoicePaymentProcessor.ts`
**Priority:** CRITICAL
**Complexity:** MEDIUM (10-15 dev hours)

**Enhancement Details:**
- Implement timezone-aware date conversion for payment dates
- Handle complex payment structure: `payment.invoicePayments` array processing
- Add proper timezone configuration management

### 3. HIGH: Appointment Custom Field Synchronization

**Missing Functions:**
- `syncAppointmentCustomfieldsToAp()` - Complete implementation missing
- `syncApToCcAppointmentCustomfields()` - Complete implementation missing

**Files to Create/Modify:**
- `New/src/helpers/appointmentCustomFields.ts` (NEW FILE)
- `New/src/processors/appointmentProcessor.ts` (MODIFY - add custom field sync)

#### Task 3.1: Create Appointment Custom Field Sync to AP
**File:** `New/src/helpers/appointmentCustomFields.ts`
**Priority:** HIGH
**Complexity:** MEDIUM (15-20 dev hours)

```typescript
/**
 * Replicates v3Integration's syncAppointmentCustomfieldsToAp() function
 * - Extracts appointment service, practitioner, location, resource, category info
 * - Updates "Last appointment [field]" custom fields on AP contact
 */
export async function syncAppointmentCustomFieldsToAP(
  appointment: GetCCAppointmentType,
  patientRecord: typeof patient.$inferSelect
): Promise<void>
```

**Business Logic:**
1. Extract service IDs from appointment and fetch service names
2. Get practitioner name from appointment.practitioner
3. Get location name from appointment.location
4. Get resource names from appointment.resources
5. Get category names from appointment.categories
6. Update AP contact custom fields: "Last appointment services", "Last appointment treated by", etc.

#### Task 3.2: Create Appointment Custom Field Sync from AP
**File:** `New/src/helpers/appointmentCustomFields.ts`
**Priority:** HIGH
**Complexity:** MEDIUM (10-15 dev hours)

```typescript
/**
 * Replicates v3Integration's syncApToCcAppointmentCustomfields() function
 * - Syncs specific AP custom fields to CC appointment
 * - Fields: "AP Services", "AP People", "AP Resources", "AP Location", "AP Categories"
 */
export async function syncAPAppointmentCustomFieldsToCC(
  appointment: GetAPAppointmentType,
  patientRecord: typeof patient.$inferSelect
): Promise<void>
```

### 4. HIGH: Service/Entity Name Resolution

**Missing Functions:**
- Service name lookup functions
- Practitioner name lookup functions
- Location/Resource name lookup functions

**Files to Create:**
- `New/src/helpers/entityLookup.ts` (NEW FILE)

#### Task 4.1: Create Entity Lookup Helper Functions
**File:** `New/src/helpers/entityLookup.ts`
**Priority:** HIGH
**Complexity:** LOW (8-12 dev hours)

```typescript
/**
 * Entity lookup functions for resolving IDs to human-readable names
 */
export async function getServiceNames(serviceIds: number[]): Promise<Record<number, string>>
export async function getPractitionerName(practitionerId: number): Promise<string>
export async function getLocationName(locationId: number): Promise<string>
export async function getResourceNames(resourceIds: number[]): Promise<Record<number, string>>
export async function getCategoryNames(categoryIds: number[]): Promise<Record<number, string>>
```

**Implementation Details:**
- Use ccClient to fetch entity data
- Implement caching for performance
- Handle missing/invalid IDs gracefully
- Return empty strings for failed lookups

### 5. MEDIUM: Timezone-Aware Processing

**Files to Modify:**
- `New/src/utils/dateUtils.ts` (NEW FILE)
- `New/src/processors/invoicePaymentProcessor.ts` (MODIFY)
- `New/src/processors/appointmentProcessor.ts` (MODIFY)

#### Task 5.1: Create Timezone Utility Functions
**File:** `New/src/utils/dateUtils.ts`
**Priority:** MEDIUM
**Complexity:** LOW (5-8 dev hours)

```typescript
/**
 * Timezone-aware date processing utilities
 */
export function convertToTimezone(isoDate: string, timezone: string): string
export function formatDateForCustomField(date: Date, timezone: string): string
export function getCurrentTimezoneFromConfig(): string
```

### 6. CRITICAL: Database Integration Pattern Alignment

**Missing Database Integration Patterns:**
- Early database storage timing
- Persistent duplicate prevention
- Relationship management improvements
- Database schema enhancements

**Files to Create/Modify:**
- `New/src/database/migrations/` (NEW MIGRATION FILES)
- `New/src/database/schema.ts` (MODIFY - add missing fields)
- `New/src/storage/skipManager.ts` (NEW FILE)
- `New/src/storage/patientStorage.ts` (MODIFY - align with v3Integration logic)
- `New/src/processors/` (MODIFY ALL - change database timing)

#### Task 6.1: Database Schema Enhancements
**Files:** `New/src/database/migrations/`, `New/src/database/schema.ts`
**Priority:** CRITICAL
**Complexity:** MEDIUM (15-20 dev hours)

**Schema Changes Required:**
```sql
-- Add missing fields to patients table
ALTER TABLE patients ADD COLUMN source VARCHAR(10) DEFAULT 'cc';
ALTER TABLE patients ADD UNIQUE CONSTRAINT patients_email_unique (email);
ALTER TABLE patients ADD UNIQUE CONSTRAINT patients_phone_unique (phone);

-- Add missing fields to appointments table
ALTER TABLE appointments ADD COLUMN contact_id VARCHAR(255);
ALTER TABLE appointments ADD COLUMN ap_note VARCHAR(255);
ALTER TABLE appointments ADD COLUMN source VARCHAR(10) DEFAULT 'cc';

-- Create skips table for persistent duplicate prevention
CREATE TABLE skips (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  action VARCHAR(255) NOT NULL,
  cc_id INTEGER,
  ap_id VARCHAR(255),
  created_at TIMESTAMP DEFAULT now(),
  updated_at TIMESTAMP DEFAULT now()
);
```

**Implementation Details:**
- Create migration files for schema changes
- Update Drizzle schema definitions
- Add proper indexes for performance
- Implement foreign key constraints

#### Task 6.2: Persistent Skip/Duplicate Prevention System
**File:** `New/src/storage/skipManager.ts`
**Priority:** CRITICAL
**Complexity:** MEDIUM (10-15 dev hours)

```typescript
/**
 * Persistent duplicate prevention system (equivalent to v3Integration Skip model)
 */
export async function hasProcessPatientCreate(apId: string): Promise<boolean>
export async function putProcessPatientCreate(apId: string): Promise<void>
export async function hasProcessPatientUpdate(ccId: number): Promise<boolean>
export async function putProcessPatientUpdate(ccId: number): Promise<void>
export async function hasProcessAppointmentUpdate(ccId: number): Promise<boolean>
export async function putProcessAppointmentUpdate(ccId: number): Promise<void>
export async function hasProcessAppointmentCreate(apId: string): Promise<boolean>
export async function putProcessAppointmentCreate(apId: string): Promise<void>
```

**Business Logic:**
- Replicate v3Integration Skip model functionality
- Auto-delete records after check (self-cleaning)
- Support different action types
- Persist across server restarts and deployments

#### Task 6.3: Enhanced Patient Storage with v3Integration Logic
**File:** `New/src/storage/patientStorage.ts`
**Priority:** CRITICAL
**Complexity:** HIGH (20-25 dev hours)

**Enhancements Required:**
- Implement combined email+phone search logic
- Add early database storage timing
- Preserve all existing data during updates
- Add source field tracking
- Implement proper relationship management

**Modified Search Logic:**
```typescript
// Replicate v3Integration combined search pattern
if (payload.apId) {
  query = query.where(eq(patient.apId, payload.apId))
} else if (payload.ccId) {
  query = query.where(eq(patient.ccId, payload.ccId))
} else {
  // Combined email+phone search like v3Integration
  const conditions = []
  if (payload.email) conditions.push(eq(patient.email, payload.email))
  if (payload.phone) conditions.push(eq(patient.phone, payload.phone))
  if (conditions.length > 0) {
    query = query.where(or(...conditions))
  }
}
```

#### Task 6.4: Database Timing Alignment in Processors
**Files:** All processor files in `New/src/processors/`
**Priority:** CRITICAL
**Complexity:** HIGH (25-30 dev hours)

**Changes Required:**
1. **Patient Processors**: Store to database BEFORE API calls
2. **Appointment Processors**: Store to database BEFORE API calls
3. **Invoice/Payment Processors**: Store to database BEFORE API calls

**New Processing Sequence:**
```typescript
// OLD: Buffer → API → Database
// NEW: Skip Check → Database → API → Database Update

export async function processPatientCreate(payload, context) {
  // 1. Check persistent skip system
  if (await hasProcessPatientCreate(payload.id)) return;

  // 2. Store to database FIRST (early storage)
  const dbPatient = await searchCreateOrUpdatePatient(payload);

  // 3. Process API calls using stored data
  const apResult = await createOrUpdateAPContact(dbPatient);

  // 4. Update database with API results
  await updatePatientWithAPIResults(dbPatient, apResult);

  // 5. Mark as processed in skip system
  await putProcessPatientCreate(apResult.id);
}
```

#### Task 6.5: Relationship Management Enhancements
**Files:** `New/src/database/schema.ts`, processor files
**Priority:** HIGH
**Complexity:** MEDIUM (15-20 dev hours)

**Enhancements Required:**
- Add contact_id field to appointments for AP contact references
- Add ap_note field for appointment note tracking
- Implement dual reference system (patient_id + contact_id)
- Add proper foreign key constraints

**Relationship Updates:**
```typescript
// Enhanced appointment creation with dual references
const appointmentData = {
  ccId: payload.id,
  patientId: dbPatient.id,        // Local patient reference
  contactId: apContact.id,        // AP contact reference
  ccData: payload,
  apData: apAppointment,
  apNote: noteId,                 // AP note reference
  source: 'cc'
}
```

### 7. LOW: Note Management

**Files to Create/Modify:**
- `New/src/helpers/noteManager.ts` (NEW FILE)
- `New/src/processors/appointmentProcessor.ts` (MODIFY)

#### Task 7.1: Create Note Management Functions
**File:** `New/src/helpers/noteManager.ts`
**Priority:** LOW
**Complexity:** LOW (5-8 dev hours)

```typescript
/**
 * AP note management for appointments
 */
export async function createAppointmentNote(contactId: string, appointmentData: GetCCAppointmentType): Promise<string>
export async function updateAppointmentNote(noteId: string, contactId: string, message: string): Promise<void>
export async function createCancellationNote(noteId: string, contactId: string, appointment: GetCCAppointmentType): Promise<void>
```

## Implementation Sequence Recommendations

### Phase 1: Critical Database Foundation (Weeks 1-2)
1. Task 6.1: Database Schema Enhancements
2. Task 6.2: Persistent Skip/Duplicate Prevention System
3. Task 6.3: Enhanced Patient Storage with v3Integration Logic

### Phase 2: Database Integration Alignment (Weeks 3-4)
4. Task 6.4: Database Timing Alignment in Processors
5. Task 6.5: Relationship Management Enhancements
6. Task 1.1: Service Appointment Counting Algorithm

### Phase 3: Advanced Data Processing (Weeks 5-6)
7. Task 1.2: Service Spending Calculation Algorithm
8. Task 2.1: Enhanced LTV Calculation
9. Task 2.2: Enhanced Invoice Processing

### Phase 4: Financial and Appointment Processing (Weeks 7-8)
10. Task 2.3: Enhanced Payment Processing
11. Task 1.3: Integrate Service Calculations
12. Task 4.1: Entity Lookup Functions

### Phase 5: Appointment Enhancements (Weeks 9-10)
13. Task 3.1: Appointment Custom Field Sync to AP
14. Task 3.2: Appointment Custom Field Sync from AP
15. Task 5.1: Timezone Utilities

### Phase 6: Polish and Optimization (Week 11)
16. Task 7.1: Note Management
17. Integration testing and optimization
18. Performance tuning and monitoring

## Estimated Total Implementation Time

- **Critical Database Tasks:** 70-90 developer hours
- **Critical Data Flow Tasks:** 80-105 developer hours
- **High Priority Tasks:** 40-55 developer hours
- **Medium/Low Priority Tasks:** 20-30 developer hours
- **Total:** 210-280 developer hours (26-35 working days)

**Note:** The addition of critical database integration tasks significantly increases the implementation scope, but these are essential for maintaining data consistency and preventing data loss scenarios that could occur with the current New directory implementation.

## Success Criteria

1. **Service Calculations:** Custom fields like "Total appointments booked for [Service]" and "Total amount paid for [Service]" are accurately calculated and synced
2. **Financial Processing:** LTV calculations match v3Integration logic with proper payment validation
3. **Appointment Sync:** Appointment custom fields are properly synced in both directions (CC ↔ AP)
4. **Data Integrity:** All data transformations maintain accuracy and handle edge cases
5. **Performance:** New implementations maintain or improve upon v3Integration performance
6. **Error Handling:** Comprehensive error handling and logging for all new functionality

## Critical Database Integration Gaps

### Database Usage Pattern Analysis

The analysis reveals fundamental differences in how v3Integration and the New directory handle local database integration, which significantly impacts data flow architecture and consistency.

#### 1. Data Storage Timing Differences

**v3Integration Pattern:**
- **Early Database Storage**: Stores data to local database FIRST, then processes API calls
- **Sequence**: Webhook → Skip Check → Contact.searchCreateOrUpdate() → updateOrCreateContact() → API Calls
- **Database as Source of Truth**: Local database acts as the primary data store during processing

**New Directory Pattern:**
- **Late Database Storage**: Processes API calls FIRST, then stores results to database
- **Sequence**: Webhook → Buffer Check → API Calls → Database Storage
- **API as Source of Truth**: External APIs are treated as primary data source

**Critical Impact**: This timing difference can lead to data inconsistency if API calls fail after database updates in v3Integration vs before database updates in New directory.

#### 2. Data Storage Purpose Differences

**v3Integration Database Purposes:**
- **Relationship Hub**: Central point for managing patient-appointment relationships
- **Data Caching**: Stores complete AP and CC data objects for offline access
- **State Management**: Tracks sync state and processing history
- **Duplicate Prevention**: Uses Skip model for persistent duplicate detection
- **Audit Trail**: Maintains complete history of all data changes

**New Directory Database Purposes:**
- **Result Storage**: Stores final results after successful API operations
- **Performance Optimization**: Caches data for faster subsequent access
- **Basic Relationships**: Simple foreign key relationships
- **Error Logging**: Comprehensive error tracking and monitoring

**Critical Gap**: New directory lacks the relationship hub functionality and state management capabilities.

#### 3. Database Schema Critical Differences

**v3Integration Schema (MySQL):**
```sql
contacts:
- id (primary key)
- source ('ap' | 'cc')
- ap_id (unique, nullable)
- cc_id (unique, nullable)
- email (unique, nullable)
- phone (unique, nullable)
- ap_data (JSON - complete AP contact data)
- cc_data (JSON - complete CC patient data)

appointments:
- id (primary key)
- cp_id (foreign key to contacts.id)
- source ('ap' | 'cc')
- cc_id (unique, nullable)
- ap_id (unique, nullable)
- patient_id (CC patient ID)
- contact_id (AP contact ID)
- ap_data (JSON - complete AP appointment data)
- cc_data (JSON - complete CC appointment data)
- ap_note (AP note ID for appointment)

skips:
- id (primary key)
- action (operation type)
- cc_id (nullable)
- ap_id (nullable)
```

**New Directory Schema (PostgreSQL):**
```sql
patients:
- id (UUID primary key)
- ap_id (unique, nullable)
- cc_id (unique, nullable)
- email (nullable)
- phone (nullable)
- ap_data (JSONB)
- cc_data (JSONB)
- ap_updated_at (timestamp)
- cc_updated_at (timestamp)

appointments:
- id (UUID primary key)
- ap_id (unique, nullable)
- cc_id (unique, nullable)
- patient_id (foreign key to patients.id)
- ap_data (JSONB)
- cc_data (JSONB)
- ap_updated_at (timestamp)
- cc_updated_at (timestamp)

error_logs:
- id (UUID primary key)
- type (error type)
- message (error message)
- stack (error stack trace)
- data (JSONB metadata)
```

**Missing Critical Elements:**
- No `source` field tracking data origin
- No `cp_id` relationship field in appointments
- No `contact_id` field for AP contact references
- No `ap_note` field for note management
- No persistent duplicate prevention mechanism (Skip model equivalent)
- No unique constraints on email/phone fields

#### 4. Data Persistence Strategy Differences

**v3Integration Contact.searchCreateOrUpdate():**
```typescript
// Priority: apId → ccId → email+phone (combined search)
let contact = Contact.query()
if (payload.apId) {
  contact = contact.where('apId', payload.apId)
} else if (payload.ccId) {
  contact = contact.where('ccId', payload.ccId)
} else {
  if (payload.email) contact = contact.where('email', payload.email)
  if (payload.phone) contact = contact.where('phone', payload.phone)
}
```

**New Directory searchCreateOrUpdatePatient():**
```typescript
// Priority: apId → ccId → email → phone (separate searches)
if (payload.apId) {
  existingPatient = await findPatientByAPId(payload.apId)
} else if (payload.ccId) {
  existingPatient = await findPatientByCCId(payload.ccId)
} else if (payload.email) {
  existingPatient = await findPatientByEmail(payload.email)
} else if (payload.phone) {
  existingPatient = await findPatientByPhone(payload.phone)
}
```

**Critical Differences:**
- v3Integration combines email+phone in single query for better matching
- New directory uses separate searches which may miss combined matches
- v3Integration preserves all existing data during updates
- New directory may overwrite fields unintentionally

#### 5. Buffer/Skip Management Critical Differences

**v3Integration Skip Model:**
- **Persistent Storage**: MySQL table with action, cc_id, ap_id
- **Automatic Cleanup**: Records are deleted after check (self-cleaning)
- **Action-Specific**: Different skip types for different operations
- **Cross-Request Persistence**: Survives server restarts and deployments

**New Directory Buffer System:**
- **In-Memory Storage**: Map-based buffer with timestamp expiration
- **Time-Based Expiration**: 60-second default buffer window
- **Request-Scoped**: Lost on server restart or deployment
- **Generic Keys**: Single buffer key format for all operations

**Critical Implications:**
- New directory buffer is not persistent across deployments
- No protection against duplicate processing during server restarts
- Different buffer strategies may lead to different duplicate detection behavior

#### 6. Relationship Management Differences

**v3Integration Relationships:**
- **Explicit cp_id**: Direct foreign key from appointments to contacts
- **Dual References**: Both patient_id (CC) and contact_id (AP) in appointments
- **Relationship Queries**: Can query appointments through contact relationships
- **Data Integrity**: Foreign key constraints ensure referential integrity

**New Directory Relationships:**
- **Single patient_id**: Only references patients table
- **No Dual References**: Missing contact_id field for AP appointments
- **Simplified Structure**: Less complex but potentially less flexible
- **UUID-Based**: Uses UUIDs instead of auto-increment integers

**Missing Relationship Patterns:**
- No way to directly link appointments to AP contacts
- No cp_id equivalent for local relationship management
- Missing appointment note tracking (ap_note field)

#### 7. Data Synchronization Point Differences

**v3Integration Sync Points:**
1. **Job Start**: Database read to check existing records
2. **Early Storage**: Contact.searchCreateOrUpdate() stores data immediately
3. **API Processing**: Uses stored data for API calls
4. **Result Update**: Updates database with API response data
5. **Custom Field Sync**: Additional database updates for custom fields

**New Directory Sync Points:**
1. **Webhook Start**: Buffer check (in-memory)
2. **API Processing**: Direct API calls without prior database storage
3. **Result Storage**: Database storage only after successful API operations
4. **Error Logging**: Database writes for error tracking

**Critical Missing Sync Points:**
- No early database storage for data preservation
- No intermediate state tracking during processing
- No custom field sync database operations
- No relationship updates during processing

## Risk Mitigation

1. **Data Accuracy:** Implement comprehensive unit tests comparing outputs with v3Integration
2. **Performance:** Use caching strategies for entity lookups and batch API calls where possible
3. **Error Handling:** Implement graceful degradation for failed API calls or missing data
4. **Rollback Plan:** Maintain feature flags for gradual rollout and quick rollback if needed
5. **Database Consistency:** Implement proper transaction management and rollback strategies
6. **Relationship Integrity:** Add missing foreign key constraints and relationship fields
